<?php

namespace App\Notifications;

use App\Channels\AwsSnsChannel;
use App\Services\SmsService;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class SendFinancialBidOpeningDate extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(private string $tenderNumber, private string $tenderDate)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [AwsSnsChannel::class];
    }

    public function toAwsSns($notifiable)
    {
        $message = "Financial bid opening date for Tender Number: {$this->tenderNumber} is scheduled on {$this->tenderDate}. E-Tender Portal";

        return SmsService::make($message);
    }
}
