<?php

namespace App\Notifications;

use App\Channels\AwsSnsChannel;
use App\Services\SmsService;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class SendTenderOpeningOtp extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(private $otpNumber, private $tenderNumber)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [AwsSnsChannel::class];
    }

    public function toAwsSns($notifiable)
    {
        $message = "Your OTP for tender opening is: {$this->otpNumber}. Tender Number: {$this->tenderNumber}. E-Tender Portal";

        return SmsService::make($message);
    }

}
