<?php

namespace App\Notifications;

use App\Channels\AwsSnsChannel;
use App\Services\SmsService;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class EmdAmountReceived extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(private string $tenderNumber, private string $companyName, private string $date)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [AwsSnsChannel::class];
    }

    public function toAwsSns($notifiable)
    {
        $message = "EMD amount received from {$this->companyName} for Tender Number: {$this->tenderNumber} on {$this->date}. E-Tender Portal";

        return SmsService::make($message);
    }
}
