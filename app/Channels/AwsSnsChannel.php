<?php

namespace App\Channels;

use App\Services\SmsService;
use Illuminate\Notifications\Notification;

class AwsSnsChannel
{
    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        // Get the phone number from the notifiable entity
        $phoneNumber = $notifiable->routeNotificationFor('sms', $notification);
        
        if (!$phoneNumber) {
            logger()->warning('AWS SNS Channel: No phone number found for notification', [
                'notifiable_type' => get_class($notifiable),
                'notifiable_id' => $notifiable->id ?? 'unknown',
                'notification_type' => get_class($notification)
            ]);
            return;
        }

        // Get the SMS message from the notification
        $smsMessage = $notification->toAwsSns($notifiable);
        
        if (!$smsMessage) {
            logger()->warning('AWS SNS Channel: No SMS message returned from notification', [
                'notifiable_type' => get_class($notifiable),
                'notifiable_id' => $notifiable->id ?? 'unknown',
                'notification_type' => get_class($notification)
            ]);
            return;
        }

        try {
            // Set the phone number and send the SMS
            $result = $smsMessage->to($phoneNumber)->send();
            
            logger()->info('AWS SNS Channel: SMS sent successfully', [
                'notifiable_type' => get_class($notifiable),
                'notifiable_id' => $notifiable->id ?? 'unknown',
                'notification_type' => get_class($notification),
                'phone_number' => $phoneNumber,
                'message_id' => $result['MessageId'] ?? 'unknown'
            ]);
            
            return $result;
        } catch (\Exception $e) {
            logger()->error('AWS SNS Channel: Failed to send SMS', [
                'notifiable_type' => get_class($notifiable),
                'notifiable_id' => $notifiable->id ?? 'unknown',
                'notification_type' => get_class($notification),
                'phone_number' => $phoneNumber,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);
            
            throw $e;
        }
    }
}
