<?php

namespace App\Services;

use Aws\Sns\SnsClient;
use Illuminate\Support\Facades\Config;

class SmsService
{
    public $to;
    public $message;
    public $variables = [];

    protected $snsClient;

    public function __construct(string $message = null)
    {
        $this->message = $message;

        // Initialize the SNS client
        $this->snsClient = new SnsClient([
            'version' => 'latest',
            'region' => config('services.sns.region'),
            'credentials' => [
                'key' => config('services.sns.key'),
                'secret' => config('services.sns.secret'),
            ],
        ]);
    }

    public static function make(string $message = null)
    {
        return new static($message);
    }

    public static function makeWithTemplate(string $template, array $variables = [])
    {
        $instance = new static();
        $instance->setTemplate($template, $variables);
        return $instance;
    }

    public function to(string $mobile)
    {
        if (is_null($mobile)) {
            throw new \Exception("Mobile number is required");
        }

        // Clean and format the mobile number
        $mobile = preg_replace('/[^0-9]/', '', $mobile);

        // Add country code if not present
        if (strlen($mobile) === 10) {
            $this->to = '+91' . $mobile;
        } elseif (strlen($mobile) === 12 && substr($mobile, 0, 2) === '91') {
            $this->to = '+' . $mobile;
        } elseif (strlen($mobile) === 13 && substr($mobile, 0, 3) === '+91') {
            $this->to = $mobile;
        } else {
            throw new \Exception("Invalid mobile number format: " . $mobile);
        }

        return $this;
    }

    public function setMessage(string $message)
    {
        $this->message = $message;
        return $this;
    }

    public function setTemplate(string $template, array $variables = [])
    {
        $this->variables = $variables;

        // Replace variables in template
        $message = $template;
        foreach ($variables as $key => $value) {
            $message = str_replace('{' . $key . '}', $value, $message);
        }

        $this->message = $message;
        return $this;
    }

    public function addVariables(array $variables = [])
    {
        $this->variables = array_merge($this->variables, $variables);
        return $this;
    }

    private function getFormattedMessage()
    {
        if ($this->message) {
            return $this->message;
        }

        // Fallback: create message from variables (for backward compatibility)
        $message = "E-Tender Portal: ";
        foreach ($this->variables as $key => $value) {
            $message .= "{$key}: {$value}, ";
        }

        return rtrim($message, ', ');
    }

    public function send()
    {
        if (!$this->to) {
            throw new \Exception("Phone number is required. Call to() method first.");
        }

        $message = $this->getFormattedMessage();

        if (empty($message)) {
            throw new \Exception("Message is required. Call setMessage() or setTemplate() method first.");
        }

        try {
            // Enhanced logging for SMS sending
            logger()->info('SMS Service: Attempting to send SMS', [
                'to' => $this->to,
                'message' => $message,
                'variables' => $this->variables,
                'aws_region' => config('services.sns.region'),
                'aws_key_configured' => !empty(config('services.sns.key')),
                'aws_secret_configured' => !empty(config('services.sns.secret'))
            ]);

            $result = $this->snsClient->publish([
                'Message' => $message,
                'PhoneNumber' => $this->to,
            ]);

            // Log successful SMS sending
            logger()->info('SMS Service: SMS sent successfully', [
                'to' => $this->to,
                'message_id' => $result['MessageId'] ?? 'unknown',
                'message_length' => strlen($message)
            ]);

            return $result;
        } catch (\Exception $e) {
            // Enhanced error logging with AWS-specific error detection
            $errorDetails = [
                'to' => $this->to,
                'message' => $message,
                'variables' => $this->variables,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'aws_region' => config('services.sns.region'),
                'aws_key_configured' => !empty(config('services.sns.key')),
                'aws_secret_configured' => !empty(config('services.sns.secret'))
            ];

            // Check for specific AWS SNS errors
            if (method_exists($e, 'getAwsErrorCode')) {
                $errorDetails['aws_error_code'] = $e->getAwsErrorCode();
                $errorDetails['aws_error_type'] = $e->getAwsErrorType();
            }

            // Check for spending limit or sandbox issues
            $errorMessage = $e->getMessage();
            if (strpos($errorMessage, 'spending limit') !== false) {
                $errorDetails['likely_cause'] = 'AWS SNS monthly spending limit exceeded';
            } elseif (strpos($errorMessage, 'sandbox') !== false) {
                $errorDetails['likely_cause'] = 'AWS SNS in sandbox mode - phone number not verified';
            } elseif (strpos($errorMessage, 'Invalid parameter') !== false) {
                $errorDetails['likely_cause'] = 'Invalid phone number format';
            }

            logger()->error('SMS Service: Failed to send SMS', $errorDetails);

            throw new \Exception("Failed to send SMS: " . $e->getMessage());
        }
    }
}