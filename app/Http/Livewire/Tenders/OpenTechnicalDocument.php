<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use App\Models\User;
use Livewire\Component;
use App\Services\SmsService;
use Illuminate\Support\Facades\DB;
use App\Enums\TenderStatus;

class OpenTechnicalDocument extends Component
{
    public $tenderId;
    public $otpInputs = []; // For user input only
    public $evaluators = [];
    public $show = false;

    protected $listeners = [
        'refreshTenders'
    ];

    public function mount($tender)
    {
        $this->tenderId = $tender;

        // Get the authenticated user directly instead of using property accessor
        $user = auth()->user();

        // Ensure user is authenticated and has a department
        if (!$user || !$user->department_id) {
            abort(403, 'Unauthorized access or missing department information.');
        }

        $this->tender = Tender::with('evaluators')
            ->where('department_id', $user->department_id)
            ->findOrFail($tender);

        $this->evaluators = $this->tender->evaluators()->get()->toArray();

        // Check if evaluators are assigned
        if (empty($this->evaluators)) {
            // Check if there are evaluators available in the department
            $availableEvaluators = User::where('role', User::ROLE_EVALUATOR)
                ->where('department_id', $this->tender->department_id)
                ->count();

            logger()->warning('Technical Bid Opening: No evaluators assigned to tender', [
                'tender_id' => $this->tender->id,
                'tender_uin' => $this->tender->tender_uin,
                'department_id' => $this->tender->department_id,
                'available_evaluators_in_department' => $availableEvaluators
            ]);

            if ($availableEvaluators > 0) {
                session()->flash('error', "No evaluators are assigned to this tender. There are {$availableEvaluators} evaluators available in your department. Please assign them in the tender edit page before opening technical bids.");
            } else {
                session()->flash('error', 'No evaluators are available in your department. Please create evaluator users first, then assign them to this tender.');
            }

            return redirect()->route('tenders.edit', ['tender' => $this->tender->id]);
        }

        $this->sendOtps();
    }

    private function sendOtps()
    {
        $otpGenerated = [];

        try {
            DB::transaction(function () use (&$otpGenerated) {
                logger()->info('Technical Bid Opening: Starting OTP generation', [
                    'tender_id' => $this->tender->id,
                    'tender_uin' => $this->tender->tender_uin,
                    'evaluators_count' => count($this->evaluators),
                    'evaluators' => $this->evaluators
                ]);

                foreach ($this->evaluators as $evaluator) {
                    $otp = mt_rand(111111, 999999);
                    $otpGenerated[$evaluator['id']] = $otp; // Store in local variable, not public property

                    // Validate evaluator phone number
                    if (empty($evaluator['phone'])) {
                        logger()->error('Technical Bid Opening: Missing phone number for evaluator', [
                            'evaluator_id' => $evaluator['id'],
                            'evaluator' => $evaluator
                        ]);
                        throw new \Exception("Phone number missing for evaluator ID: " . $evaluator['id']);
                    }

                    logger()->info('Technical Bid Opening: Sending OTP to evaluator', [
                        'evaluator_id' => $evaluator['id'],
                        'evaluator_phone' => $evaluator['phone'],
                        'otp' => $otp,
                        'tender_uin' => $this->tender->tender_uin
                    ]);

                    $message = "Your OTP for technical bid opening is: {$otp}. Tender Number: {$this->tender->tender_uin}. E-Tender Portal";

                    SmsService::make($message)
                        ->to($evaluator['phone'])
                        ->send();

                    logger()->info('Technical Bid Opening: OTP sent successfully to evaluator', [
                        'evaluator_id' => $evaluator['id'],
                        'evaluator_phone' => $evaluator['phone']
                    ]);
                }

                logger()->info('Technical Bid Opening: Generated OTPs', $otpGenerated);

                $this->tender->update([
                    'tender_technical_opening_otps' => json_encode($otpGenerated)
                ]);

                logger()->info('Technical Bid Opening: Updated tender with OTPs', [
                    'tender_id' => $this->tender->id,
                    'otps_stored' => $this->tender->tender_technical_opening_otps
                ]);
            });
        } catch (\Exception $e) {
            $this->notify('Failed to send OTPs to evaluators. Please check evaluator phone numbers and try again.', 'error');
            logger()->error('Technical Bid Opening: Error in sending OTPs', [
                'tender_id' => $this->tender->id,
                'tender_uin' => $this->tender->tender_uin,
                'evaluators' => $this->evaluators,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString()
            ]);
        }

        $this->resetErrorBag();
        $this->show = true;
    }

    public function verifyOtp()
    {   
        $storedOtps = json_decode($this->tender->tender_technical_opening_otps, true);
        logger()->info('Stored Otps: ', $storedOtps);
        // Check if $storedOtps is not null and is an array
        if (is_null($storedOtps) || !is_array($storedOtps)) {
            $this->notify('Invalid OTP data.', 'error');
            return;
        }

        $this->validate([
            'otpInputs.*' => ['required', 'digits:6', function ($attribute, $value, $fail) use ($storedOtps) {
                $evaluatorId = explode('.', $attribute)[1];

                if (!isset($storedOtps[$evaluatorId]) || $value != $storedOtps[$evaluatorId]) {
                    return $fail('The OTP for evaluator ' . $evaluatorId . ' is invalid.');
                }
            }]
        ]);

        try {
            $this->tender->update([
                'tender_technical_opening_otps' => null,
                'technicalbid_opening_date' => now(),
                'tender_status' => TenderStatus::TECHNICAL->value
            ]);

            $this->show = false;
            $this->emit('refreshBiddings');

            $this->notify('Tender opened for technical bidding.');
            return redirect()->route('tenders.show', ['tender' => $this->tender->id]);
        } catch (\Exception $e) {
            $this->notify('Something went wrong.', 'error');
        }
    }


    public function closeModal() {
        $this->show = false;
        return redirect()->route('tenders.show', ['tender' => $this->tender->id]);
    }

    public function getTenderProperty()
    {
        return Tender::findOrFail($this->tenderId);
    }

    public function getUserProperty()
    {
        return auth()->user();
    }

    public function render()
    {
        return view('livewire.tenders.open-technical-document', [
            'evaluators' => $this->evaluators
        ]);
    }
}