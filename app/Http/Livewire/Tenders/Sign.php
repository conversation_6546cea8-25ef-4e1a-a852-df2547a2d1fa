<?php

namespace App\Http\Livewire\Tenders;

use App\Models\Tender;
use Livewire\Component;

class Sign extends Component
{
    public $tender;
    public $tenderId;

    public function mount(Tender $tender)
    {
        $this->tender = $tender;
        $this->tenderId = $tender->id;
    }

    public function getCompaniesProperty()
    {
        return $this->tender->acceptedBiddings->pluck('company_name', 'id')->all();
    }

    public function render()
    {
        return view('livewire.tenders.sign');
    }
}
